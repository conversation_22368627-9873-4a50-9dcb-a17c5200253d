{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/admin/adminroute.tsx", "./src/components/admin/deskqrcodegenerator.tsx", "./src/components/auth/authpage.tsx", "./src/components/auth/loginform.tsx", "./src/components/auth/signupform.tsx", "./src/components/calendar/bookingscalendarview.tsx", "./src/components/calendar/calendarview.tsx", "./src/components/dashboard/recentbookings.tsx", "./src/components/dashboard/statscards.tsx", "./src/components/floorplan/floorplanview.tsx", "./src/components/layout/appsidebar.tsx", "./src/components/layout/layout.tsx", "./src/components/manager/managerroute.tsx", "./src/components/modals/availabledesksmodal.tsx", "./src/components/modals/bookingdialog.tsx", "./src/components/modals/daybookingsmodal.tsx", "./src/components/modals/deskdetailsmodal.tsx", "./src/components/modals/zoneinfomodal.tsx", "./src/components/temp/promotetomanager.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/logo.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/contexts/authcontext.tsx", "./src/contexts/queryprovider.tsx", "./src/hooks/use-mobile.tsx", "./src/hooks/use-toast.ts", "./src/hooks/usefloorplandata.ts", "./src/lib/calendar.ts", "./src/lib/qrcode.ts", "./src/lib/supabase.ts", "./src/lib/utils.ts", "./src/pages/bookings.tsx", "./src/pages/dashboard.tsx", "./src/pages/deskcheckin.tsx", "./src/pages/floorplan.tsx", "./src/pages/profile.tsx", "./src/pages/admin/admindashboard.tsx", "./src/pages/admin/managedesks.tsx", "./src/pages/admin/manageusers.tsx", "./src/pages/admin/managezones.tsx", "./src/pages/manager/manageteam.tsx", "./src/pages/manager/managerdashboard.tsx", "./src/types/database.ts", "./src/types/index.ts"], "version": "5.6.3"}