import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Search, 
  Calendar, 
  MapPin, 
  User
} from 'lucide-react';
import { format, subDays } from 'date-fns';
import { BookingsCalendarView } from '@/components/calendar/BookingsCalendarView';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  location?: string;
  phone?: string;
  bio?: string;
  avatar?: string;
  created_at: string;
}

interface TeamMemberStats {
  userId: string;
  totalBookings: number;
  upcomingBookings: number;
  lastBookingDate?: string;
  favoriteDesk?: string;
  favoriteZone?: string;
  weeklyBookings: number;
}

export function ManageTeam() {
  const { appUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMember, setSelectedMember] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Function to handle member selection and tab switching
  const handleViewDetails = (memberId: string) => {
    setSelectedMember(memberId);
    setActiveTab('details');
  };

  // Helper functions for booking data
  const getUpcomingBookings = () => {
    if (!selectedMemberBookings) return [];
    const today = new Date().toISOString().split('T')[0];
    return selectedMemberBookings.filter(b => b.date >= today && b.status !== 'cancelled');
  };

  const getPastBookings = () => {
    if (!selectedMemberBookings) return [];
    const today = new Date().toISOString().split('T')[0];
    return selectedMemberBookings.filter(b => b.date < today && b.status !== 'cancelled');
  };

  const formatTime = (timeString: string) => {
    const time = new Date(`2000-01-01 ${timeString}`);
    return time.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (startDate === endDate) {
      return format(start, 'MMM dd, yyyy');
    } else {
      return `${format(start, 'MMM dd')} - ${format(end, 'MMM dd, yyyy')}`;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // Fetch team members
  const { data: teamMembers, isLoading: teamLoading } = useQuery({
    queryKey: ['team-members', appUser?.id, appUser?.role],
    queryFn: async () => {
      if (!appUser?.id) throw new Error('No user ID');

      let query = supabase
        .from('users')
        .select('*');

      // If admin, show all users except admins; if manager, show only direct reports
      if (appUser.role === 'admin') {
        query = query.neq('role', 'admin');
      } else {
        query = query.eq('manager_id', appUser.id);
      }

      const { data, error } = await query.order('name');

      if (error) throw error;
      return data as TeamMember[];
    },
    enabled: !!appUser?.id && (appUser?.role === 'manager' || appUser?.role === 'admin'),
  });

  // Fetch team member statistics
  const { data: memberStats } = useQuery({
    queryKey: ['team-member-stats', appUser?.id, teamMembers],
    queryFn: async () => {
      if (!teamMembers || teamMembers.length === 0) return [];

      const oneWeekAgo = subDays(new Date(), 7).toISOString().split('T')[0];
      const today = new Date().toISOString().split('T')[0];

      const stats: TeamMemberStats[] = [];

      for (const member of teamMembers) {
        // Get all bookings for this member
        const { data: bookings } = await supabase
          .from('bookings')
          .select(`
            id,
            date,
            end_date,
            start_time,
            end_time,
            status,
            user_id,
            desk_id,
            desk:desks (
              id,
              name,
              status,
              zone_id,
              zone:zones (
                id,
                name,
                floor_number
              )
            )
          `)
          .eq('user_id', member.id)
          .order('date', { ascending: false });

        const allBookings = bookings || [];
        const activeBookings = allBookings.filter(b => b.status !== 'cancelled');
        const upcomingBookings = activeBookings.filter(b => b.date >= today);
        const weeklyBookings = activeBookings.filter(b => b.date >= oneWeekAgo);

        // Find favorite desk and zone
        const deskCounts: Record<string, number> = {};
        const zoneCounts: Record<string, number> = {};

        activeBookings.forEach(booking => {
          const deskName = (booking.desk as any)?.name;
          const zoneName = (booking.desk as any)?.zone?.name;
          
          if (deskName) {
            deskCounts[deskName] = (deskCounts[deskName] || 0) + 1;
          }
          if (zoneName) {
            zoneCounts[zoneName] = (zoneCounts[zoneName] || 0) + 1;
          }
        });

        const favoriteDesk = Object.keys(deskCounts).reduce((a, b) => 
          deskCounts[a] > deskCounts[b] ? a : b, Object.keys(deskCounts)[0]);
        const favoriteZone = Object.keys(zoneCounts).reduce((a, b) => 
          zoneCounts[a] > zoneCounts[b] ? a : b, Object.keys(zoneCounts)[0]);

        stats.push({
          userId: member.id,
          totalBookings: activeBookings.length,
          upcomingBookings: upcomingBookings.length,
          lastBookingDate: activeBookings[0]?.date,
          favoriteDesk: favoriteDesk || undefined,
          favoriteZone: favoriteZone || undefined,
          weeklyBookings: weeklyBookings.length,
        });
      }

      return stats;
    },
    enabled: !!teamMembers && teamMembers.length > 0,
  });

  // Fetch detailed bookings for selected member
  const { data: selectedMemberBookings } = useQuery({
    queryKey: ['member-bookings', selectedMember],
    queryFn: async () => {
      if (!selectedMember) return [];

      const { data: bookings, error } = await supabase
        .from('bookings')
        .select(`
          id,
          date,
          end_date,
          start_time,
          end_time,
          status,
          user_id,
          desk_id,
          desk:desks (
            id,
            name,
            status,
            zone_id,
            zone:zones (
              id,
              name,
              floor_number
            )
          )
        `)
        .eq('user_id', selectedMember)
        .order('date', { ascending: false });

      if (error) throw error;
      return bookings || [];
    },
    enabled: !!selectedMember,
  });

  // Fetch all team bookings for calendar view
  const { data: allTeamBookings } = useQuery({
    queryKey: ['team-bookings', appUser?.id, teamMembers?.map(m => m.id).sort()],
    queryFn: async () => {
      if (!teamMembers || teamMembers.length === 0) return [];

      const teamMemberIds = teamMembers.map(member => member.id);
      
      const { data: bookings, error } = await supabase
        .from('bookings')
        .select(`
          id,
          date,
          end_date,
          start_time,
          end_time,
          status,
          user_id,
          desk_id,
          desk:desks (
            id,
            name,
            status,
            zone_id,
            zone:zones (
              id,
              name,
              floor_number
            )
          )
        `)
        .in('user_id', teamMemberIds)
        .gte('date', subDays(new Date(), 30).toISOString().split('T')[0])
        .order('date', { ascending: true });

      if (error) throw error;
      
      return bookings || [];
    },
    enabled: !!teamMembers && teamMembers.length > 0,
  });

  // Filter team members based on search
  const filteredMembers = teamMembers?.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.department?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const getStatsForMember = (userId: string) => {
    return memberStats?.find(stat => stat.userId === userId);
  };

  const selectedMemberData = selectedMember 
    ? teamMembers?.find(m => m.id === selectedMember)
    : null;

  const selectedMemberStats = selectedMember 
    ? getStatsForMember(selectedMember)
    : null;

  if (appUser?.role !== 'manager' && appUser?.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Access Denied</h2>
          <p className="text-muted-foreground">Manager or admin access required</p>
        </div>
      </div>
    );
  }

  if (teamLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {appUser?.role === 'admin' ? 'Manage Organization' : 'Manage Team'}
        </h1>
        <p className="text-muted-foreground">
          {appUser?.role === 'admin'
            ? 'View and manage all organization members and their workspace activity'
            : 'View and manage your team members and their workspace activity'
          }
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Team Overview</TabsTrigger>
          <TabsTrigger value="details">Member Details</TabsTrigger>
          <TabsTrigger value="calendar">Team Calendar</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search team members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {/* Team Members Grid */}
          {filteredMembers.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredMembers.map((member) => {
                const stats = getStatsForMember(member.id);
                return (
                  <Card key={member.id} className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => handleViewDetails(member.id)}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={member.avatar || ''} alt={member.name} />
                          <AvatarFallback>
                            {member.name?.charAt(0)?.toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <CardTitle className="text-lg">{member.name}</CardTitle>
                          <CardDescription>{member.email}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Role</span>
                        <Badge variant="outline" className="capitalize">
                          {member.role}
                        </Badge>
                      </div>
                      
                      {member.department && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Department</span>
                          <span className="text-sm font-medium">{member.department}</span>
                        </div>
                      )}

                      {stats && (
                        <>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Total Bookings</span>
                            <span className="text-sm font-medium">{stats.totalBookings}</span>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">This Week</span>
                            <span className="text-sm font-medium">{stats.weeklyBookings}</span>
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Upcoming</span>
                            <span className="text-sm font-medium">{stats.upcomingBookings}</span>
                          </div>
                        </>
                      )}

                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full mt-3"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewDetails(member.id);
                        }}
                      >
                        View Details
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <Users className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium">No Team Members</h3>
              <p>No team members found matching your search</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          {selectedMemberData ? (
            <div className="space-y-6">
              {/* Member Profile Header */}
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={selectedMemberData.avatar || ''} alt={selectedMemberData.name} />
                      <AvatarFallback className="text-lg">
                        {selectedMemberData.name?.charAt(0)?.toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <CardTitle className="text-xl">{selectedMemberData.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="capitalize">
                          {selectedMemberData.role}
                        </Badge>
                        {selectedMemberData.department && (
                          <span className="text-sm text-muted-foreground">• {selectedMemberData.department}</span>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{selectedMemberData.email}</p>
                    </div>
                  </div>
                </CardHeader>
              </Card>

              {/* Quick Stats */}
              {selectedMemberStats && (
                <div className="grid grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold">{selectedMemberStats.totalBookings}</div>
                      <div className="text-xs text-muted-foreground">Total Bookings</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold">{getUpcomingBookings().length}</div>
                      <div className="text-xs text-muted-foreground">Upcoming</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold">{selectedMemberStats.weeklyBookings}</div>
                      <div className="text-xs text-muted-foreground">This Week</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold">
                        {selectedMemberStats.favoriteDesk ? selectedMemberStats.favoriteDesk.split(' ')[0] : 'None'}
                      </div>
                      <div className="text-xs text-muted-foreground">Favorite Desk</div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Upcoming Bookings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Upcoming Bookings ({getUpcomingBookings().length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {getUpcomingBookings().length > 0 ? (
                    <div className="space-y-3">
                      {getUpcomingBookings().map((booking) => (
                        <div key={booking.id} className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium">{(booking.desk as any)?.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {(booking.desk as any)?.zone?.name} • Floor {(booking.desk as any)?.zone?.floor_number}
                              </Badge>
                              <Badge className={getStatusBadgeColor(booking.status)}>
                                {booking.status}
                              </Badge>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {formatDateRange(booking.date, booking.end_date)} • {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No upcoming bookings</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Past Bookings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Past Bookings ({getPastBookings().length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {getPastBookings().length > 0 ? (
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {getPastBookings().slice(0, 20).map((booking) => (
                        <div key={booking.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900/20 rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium">{(booking.desk as any)?.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {(booking.desk as any)?.zone?.name} • Floor {(booking.desk as any)?.zone?.floor_number}
                              </Badge>
                              <Badge className={getStatusBadgeColor(booking.status)}>
                                {booking.status}
                              </Badge>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {formatDateRange(booking.date, booking.end_date)} • {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                            </div>
                          </div>
                        </div>
                      ))}
                      {getPastBookings().length > 20 && (
                        <div className="text-center text-sm text-muted-foreground py-2">
                          Showing 20 of {getPastBookings().length} past bookings
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <MapPin className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No past bookings</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <User className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium">Select a Team Member</h3>
              <p>Choose a team member from the overview tab to view their details</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="calendar" className="space-y-4">
          <BookingsCalendarView 
            bookings={(allTeamBookings || []).map(booking => ({
              id: booking.id,
              date: booking.date,
              end_date: booking.end_date,
              start_time: booking.start_time,
              end_time: booking.end_time,
              status: booking.status,
              user_id: booking.user_id,
              desk: {
                name: (booking.desk as any)?.name || 'Unknown Desk',
                zone: {
                  name: (booking.desk as any)?.zone?.name || 'Unknown Zone',
                  floor_number: (booking.desk as any)?.zone?.floor_number || 0
                }
              }
            }))}
            users={teamMembers || []}
            title="Team Booking Calendar"
            showUserColumn={true}
            isLoading={teamLoading || !allTeamBookings}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
} 