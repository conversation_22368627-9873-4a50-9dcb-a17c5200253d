import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Building2, Users, Calendar, Settings, BarChart3 } from 'lucide-react';
import { BookingsCalendarView } from '@/components/calendar/BookingsCalendarView';
import { subDays } from 'date-fns';

export function AdminDashboard() {
  // Fetch admin dashboard data
  const { data: stats, isLoading } = useQuery({
    queryKey: ['admin-stats'],
    queryFn: async () => {
      const [usersResult, desksResult, bookingsResult, zonesResult, currentBookingsResult] = await Promise.all([
        supabase.from('users').select('id, role'),
        supabase.from('desks').select('id, status'),
        supabase.from('bookings').select('id, status, created_at').gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()),
        supabase.from('zones').select('id, name'),
        supabase.from('bookings').select('desk_id, date, end_date, start_time, end_time, status').gte('end_date', new Date().toISOString().split('T')[0]).in('status', ['booked', 'checked-in'])
      ]);

      const users = usersResult.data || [];
      const desks = desksResult.data || [];
      const bookings = bookingsResult.data || [];
      const zones = zonesResult.data || [];
      const currentBookings = currentBookingsResult.data || [];

      // Calculate real-time desk status
      const { calculateRealtimeStatus } = await import('@/lib/utils');
      const realtimeStatuses = desks.map(desk => 
        calculateRealtimeStatus(desk.status, desk.id, currentBookings)
      );

      const availableDesks = realtimeStatuses.filter(status => status === 'available').length;
      const occupiedDesks = realtimeStatuses.filter(status => status === 'occupied').length;
      const maintenanceDesks = realtimeStatuses.filter(status => status === 'maintenance').length;

      return {
        totalUsers: users.length,
        adminUsers: users.filter(u => u.role === 'admin').length,
        employeeUsers: users.filter(u => u.role === 'employee').length,
        totalDesks: desks.length,
        availableDesks,
        occupiedDesks,
        maintenanceDesks,
        totalBookings: bookings.length,
        activeBookings: bookings.filter(b => b.status === 'booked' || b.status === 'checked-in').length,
        totalZones: zones.length,
      };
    },
  });

  // Fetch all users for calendar view
  const { data: allUsers } = useQuery({
    queryKey: ['all-users-for-calendar'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, avatar, role, department')
        .neq('role', 'admin')
        .order('name');

      if (error) throw error;
      return data || [];
    },
  });

  // Fetch all bookings for calendar view
  const { data: allBookings } = useQuery({
    queryKey: ['all-bookings-for-calendar'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          date,
          end_date,
          start_time,
          end_time,
          status,
          user_id,
          desk_id,
          desk:desks (
            id,
            name,
            status,
            zone_id,
            zone:zones (
              id,
              name,
              floor_number
            )
          )
        `)
        .gte('date', subDays(new Date(), 30).toISOString().split('T')[0])
        .order('date', { ascending: true });

      if (error) throw error;
      return data || [];
    },
  });

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Manage your workspace and monitor system activity
        </p>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="calendar" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Organization Calendar
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          {/* Stats Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalUsers || 0}</div>
                <div className="flex gap-2 mt-2">
                  <Badge variant="secondary">{stats?.adminUsers || 0} Admins</Badge>
                  <Badge variant="outline">{stats?.employeeUsers || 0} Employees</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Desk Utilization</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalDesks || 0}</div>
                <div className="flex gap-1 mt-2 text-xs">
                  <Badge variant="default" className="bg-green-500">
                    {stats?.availableDesks || 0} Available
                  </Badge>
                  <Badge variant="default" className="bg-orange-500">
                    {stats?.occupiedDesks || 0} Occupied
                  </Badge>
                  <Badge variant="default" className="bg-red-500">
                    {stats?.maintenanceDesks || 0} Maintenance
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Bookings (30 days)</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalBookings || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.activeBookings || 0} currently active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Zones Configured</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalZones || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Active workspace zones
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common administrative tasks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex flex-col gap-2">
                  <a href="/admin/zones" className="text-primary hover:underline">→ Manage Zones</a>
                  <a href="/admin/desks" className="text-primary hover:underline">→ Configure Desks</a>
                  <a href="/admin/users" className="text-primary hover:underline">→ Manage Users</a>
                  <a href="/admin/settings" className="text-primary hover:underline">→ System Settings</a>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Health</CardTitle>
                <CardDescription>
                  Current system status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Database</span>
                    <Badge variant="default" className="bg-green-500">Healthy</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Authentication</span>
                    <Badge variant="default" className="bg-green-500">Active</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Desk Availability</span>
                    <Badge variant="default" className="bg-green-500">
                      {Math.round(((stats?.availableDesks || 0) / (stats?.totalDesks || 1)) * 100)}%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="calendar" className="mt-6">
          <BookingsCalendarView
            bookings={(allBookings || []).map(booking => ({
              id: booking.id,
              date: booking.date,
              end_date: booking.end_date,
              start_time: booking.start_time,
              end_time: booking.end_time,
              status: booking.status,
              user_id: booking.user_id,
              desk: {
                name: (booking.desk as any)?.name || 'Unknown Desk',
                zone: {
                  name: (booking.desk as any)?.zone?.name || 'Unknown Zone',
                  floor_number: (booking.desk as any)?.zone?.floor_number || 0
                }
              }
            }))}
            users={allUsers || []}
            title="Organization Booking Calendar"
            showUserColumn={true}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
} 