import {
  Building2,
  Calendar,
  LayoutDashboard,
  MapPin,
  Settings,
  Users,
  User,
  BarChart3,
  Moon,
  Sun,
  LogOut,
  UserCheck,
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { useAuth } from '@/contexts/AuthContext';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Link, useLocation } from 'react-router-dom';
import { Logo } from '@/components/ui/logo';

export function AppSidebar() {
  const { appUser, signOut } = useAuth();
  const { theme, setTheme } = useTheme();
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  const employeeItems = [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      title: 'Book a Desk',
      url: '/floor-plan',
      icon: MapPin,
    },
    {
      title: 'My Bookings',
      url: '/bookings',
      icon: Calendar,
    },
    {
      title: 'Profile',
      url: '/profile',
      icon: User,
    },
  ];

  const managerItems = [
    {
      title: 'Team Dashboard',
      url: '/manager',
      icon: BarChart3,
    },
    {
      title: 'Manage Team',
      url: '/manager/team',
      icon: UserCheck,
    },
  ];

  const adminItems = [
    {
      title: 'Admin Dashboard',
      url: '/admin',
      icon: BarChart3,
    },
    {
      title: 'Manage Zones',
      url: '/admin/zones',
      icon: MapPin,
    },
    {
      title: 'Manage Desks',
      url: '/admin/desks',
      icon: Building2,
    },
    {
      title: 'Manage Users',
      url: '/admin/users',
      icon: Users,
    },
    {
      title: 'Settings',
      url: '/admin/settings',
      icon: Settings,
    },
  ];

  const getMenuGroups = () => {
    const groups = [];
    
    // Main navigation for all users
    groups.push({
      label: 'Navigation',
      items: employeeItems
    });

    // Manager section
    if (appUser?.role === 'manager' || appUser?.role === 'admin') {
      groups.push({
        label: 'Team Management',
        items: managerItems
      });
    }

    // Admin section
    if (appUser?.role === 'admin') {
      groups.push({
        label: 'Administration',
        items: adminItems
      });
    }

    return groups;
  };

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <div className="flex flex-col items-center gap-2 px-2 py-3">
          <div className="flex h-32 w-32 items-center justify-center">
            <Logo className="h-32 w-32 object-contain" />
          </div>
          <div className="flex flex-col items-center text-center">
            <span className="text-xl font-bold">SymDesk</span>
            <span className="text-sm text-muted-foreground">By Symplexity</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {getMenuGroups().map((group) => (
          <SidebarGroup key={group.label}>
            <SidebarGroupLabel>{group.label}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive(item.url)}
                      tooltip={item.title}
                    >
                      <Link to={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton size="lg" className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={appUser?.avatar} alt={appUser?.name} />
                    <AvatarFallback className="rounded-lg">
                      {appUser?.name?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{appUser?.name}</span>
                    <span className="truncate text-xs capitalize">{appUser?.role}</span>
                  </div>
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
                  {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                  <span>Toggle theme</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link to="/profile">
                    <User className="h-4 w-4" />
                    <span>Profile</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={signOut}>
                  <LogOut className="h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}