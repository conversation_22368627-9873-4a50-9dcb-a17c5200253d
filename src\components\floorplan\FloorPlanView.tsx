import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
// import { ZoneInfoModal } from '@/components/modals/ZoneInfoModal';
import { Building2, Filter, CalendarIcon, CheckCircle2, Clock, Wrench, User, Mail, Phone, MapPin, Info, ChevronLeft, ChevronRight } from 'lucide-react';
import { format, addDays, subDays } from 'date-fns';
import { cn } from '@/lib/utils';
import { DeskWithZone } from '@/types';
import { useState } from 'react';
import React from 'react';

interface FloorPlanViewProps {
  isLoading: boolean;
  groupedData: Record<number, Record<string, { zone: any; desks: DeskWithZone[] }>>;
  availableFloors: number[];
  selectedFloor: string | 'all';
  setSelectedFloor: (floor: string | 'all') => void;
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  isDateCalendarOpen: boolean;
  setIsDateCalendarOpen: (open: boolean) => void;
  getStatusForDate: (desk: DeskWithZone, date: Date) => string;
  getBookingForDate: (desk: DeskWithZone, date: Date) => any;
  getBookingCountForDate: (date: Date) => number;
  getTotalDeskCount: () => number;
  isDateDisabled: (date: Date) => boolean;
  handleDeskClick: (desk: DeskWithZone) => void;
}

export function FloorPlanView({
  isLoading,
  groupedData,
  availableFloors,
  selectedFloor,
  setSelectedFloor,
  selectedDate,
  setSelectedDate,
  isDateCalendarOpen,
  setIsDateCalendarOpen,
  getStatusForDate,
  getBookingForDate,
  getBookingCountForDate,
  getTotalDeskCount,
  isDateDisabled,
  handleDeskClick,
}: FloorPlanViewProps) {
  const [selectedProfile, setSelectedProfile] = useState<{desk: DeskWithZone, booking: any} | null>(null);
  const [selectedZoneInfo, setSelectedZoneInfo] = useState<any | null>(null);
  const [isZoneInfoModalOpen, setIsZoneInfoModalOpen] = useState(false);

  // Filter data based on selected floor
  const filteredData = selectedFloor === 'all' 
    ? groupedData 
    : { [parseInt(selectedFloor)]: groupedData[parseInt(selectedFloor)] || {} };

  // Enhanced status styling with gradients and icons
  const getEnhancedStatusStyling = (status: string) => {
    switch (status) {
      case 'available':
        return {
          background: 'bg-gradient-to-br from-emerald-400 to-emerald-600',
          icon: <CheckCircle2 className="w-4 h-4 absolute top-2 right-2 opacity-80" />,
          pulse: false,
          border: 'ring-emerald-300'
        };
      case 'occupied':
        return {
          background: 'bg-gradient-to-br from-rose-400 to-rose-600',
          icon: <User className="w-4 h-4 absolute top-2 right-2 opacity-80" />,
          pulse: true,
          border: 'ring-rose-300'
        };
      case 'maintenance':
        return {
          background: 'bg-gradient-to-br from-amber-400 to-amber-600',
          icon: <Wrench className="w-4 h-4 absolute top-2 right-2 opacity-80" />,
          pulse: false,
          border: 'ring-amber-300'
        };
      default:
        return {
          background: 'bg-gradient-to-br from-slate-400 to-slate-600',
          icon: <Clock className="w-4 h-4 absolute top-2 right-2 opacity-80" />,
          pulse: false,
          border: 'ring-slate-300'
        };
    }
  };

  const handleDeskClickInternal = (desk: DeskWithZone) => {
    const status = getStatusForDate(desk, selectedDate);
    const booking = getBookingForDate(desk, selectedDate);
    
    if (status === 'occupied' && booking?.users) {
      // Show profile modal for occupied desks
      setSelectedProfile({ desk, booking });
    } else {
      // Use existing desk booking flow for available desks
      handleDeskClick(desk);
    }
  };

  const handleZoneInfoClick = (zone: any, desks: any[]) => {
    setSelectedZoneInfo({ ...zone, desks: [{ count: desks.length }] });
    setIsZoneInfoModalOpen(true);
  };

  const handlePreviousDay = () => {
    const previousDay = subDays(selectedDate, 1);
    if (!isDateDisabled(previousDay)) {
      setSelectedDate(previousDay);
    }
  };

  const handleNextDay = () => {
    const nextDay = addDays(selectedDate, 1);
    if (!isDateDisabled(nextDay)) {
      setSelectedDate(nextDay);
    }
  };

  if (isLoading) {
    return (
      <Card className="shadow-clay-sm">
        <CardHeader>
          <div className="h-6 bg-muted animate-pulse rounded" />
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      {/* Profile Modal */}
      <Dialog open={!!selectedProfile} onOpenChange={(open) => !open && setSelectedProfile(null)}>
        <DialogContent className="max-w-md mx-4 sm:mx-auto">
          {selectedProfile && (
            <>
              <DialogHeader>
                <DialogTitle className="text-lg sm:text-xl">Desk Occupant</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 sm:space-y-6">
                {/* Header with background */}
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 h-16 sm:h-20 relative rounded-lg overflow-hidden">
                  <div className="absolute inset-0 bg-black/10"></div>
                </div>
                
                {/* Profile Content */}
                <div className="relative px-1 sm:px-2">
                  {/* Avatar positioned over header */}
                  <div className="flex justify-center -mt-12 sm:-mt-16 mb-3 sm:mb-4">
                    <Avatar className="w-16 h-16 sm:w-20 sm:h-20 ring-4 ring-white dark:ring-gray-800 shadow-xl">
                      <AvatarImage 
                        src={selectedProfile.booking.users.avatar} 
                        alt={selectedProfile.booking.users.name}
                        className="object-cover"
                      />
                      <AvatarFallback className="text-lg sm:text-xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                        {selectedProfile.booking.users.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                  
                  {/* User Info */}
                  <div className="text-center space-y-2">
                    <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">
                      {selectedProfile.booking.users.name}
                    </h3>
                    {selectedProfile.booking.users.role && (
                      <Badge variant="secondary" className="capitalize text-xs sm:text-sm">
                        {selectedProfile.booking.users.role}
                      </Badge>
                    )}
                  </div>
                  
                  {/* Contact Info */}
                  {(selectedProfile.booking.users.email || selectedProfile.booking.users.phone || selectedProfile.booking.users.location) && (
                    <div className="mt-3 sm:mt-4 space-y-2">
                      {selectedProfile.booking.users.email && (
                        <div className="flex items-center gap-2 text-xs sm:text-sm">
                          <Mail className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
                          <span className="text-muted-foreground truncate">{selectedProfile.booking.users.email}</span>
                        </div>
                      )}
                      {selectedProfile.booking.users.phone && (
                        <div className="flex items-center gap-2 text-xs sm:text-sm">
                          <Phone className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
                          <span className="text-muted-foreground">{selectedProfile.booking.users.phone}</span>
                        </div>
                      )}
                      {selectedProfile.booking.users.location && (
                        <div className="flex items-center gap-2 text-xs sm:text-sm">
                          <MapPin className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
                          <span className="text-muted-foreground truncate">{selectedProfile.booking.users.location}</span>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Booking Info */}
                  <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-200 dark:border-gray-700 space-y-2">
                    <div className="flex items-center justify-between text-xs sm:text-sm">
                      <span className="text-muted-foreground">Desk:</span>
                      <span className="font-medium truncate ml-2">{selectedProfile.desk.name}</span>
                    </div>
                    <div className="flex items-center justify-between text-xs sm:text-sm">
                      <span className="text-muted-foreground">Date:</span>
                      <span className="font-medium ml-2">
                        <span className="hidden sm:inline">{format(selectedDate, "MMM dd, yyyy")}</span>
                        <span className="sm:hidden">{format(selectedDate, "MMM dd")}</span>
                      </span>
                    </div>
                    {selectedProfile.booking.start_time && selectedProfile.booking.end_time && (
                      <div className="flex items-center justify-between text-xs sm:text-sm">
                        <span className="text-muted-foreground">Time:</span>
                        <span className="font-medium ml-2">
                          {selectedProfile.booking.start_time} - {selectedProfile.booking.end_time}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {/* Status indicator */}
                  <div className="mt-3 sm:mt-4 flex items-center justify-center">
                    <div className="flex items-center gap-2 px-2 sm:px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs sm:text-sm font-medium">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="hidden sm:inline">Currently occupied</span>
                      <span className="sm:hidden">Occupied</span>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Date and Floor Filters */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-3 md:gap-4">
        {/* Date Filter */}
        <Card className="shadow-clay-sm">
          <CardHeader className="pb-2 md:pb-3">
            <CardTitle className="flex items-center gap-2 text-sm sm:text-base md:text-lg">
              <CalendarIcon className="h-4 w-4 md:h-5 md:w-5" />
              Select Date
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="space-y-2 sm:space-y-3">
              <div className="flex items-center gap-1 sm:gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviousDay}
                  disabled={isDateDisabled(subDays(selectedDate, 1))}
                  className="h-7 w-7 sm:h-8 sm:w-8 md:h-10 md:w-10 p-0 flex-shrink-0"
                  title="Previous day"
                >
                  <ChevronLeft className="h-3 w-3 md:h-4 md:w-4" />
                </Button>
                
                <Popover open={isDateCalendarOpen} onOpenChange={setIsDateCalendarOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-start text-left font-normal flex-1 min-w-0 h-7 sm:h-8 md:h-10 text-xs sm:text-sm",
                        !selectedDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-1 sm:mr-2 h-3 w-3 md:h-4 md:w-4 flex-shrink-0" />
                      <span className="truncate">
                        {selectedDate ? (
                          <>
                            <span className="hidden md:inline">{format(selectedDate, "EEEE, MMM dd, yyyy")}</span>
                            <span className="hidden sm:inline md:hidden">{format(selectedDate, "EEE, MMM dd")}</span>
                            <span className="sm:hidden">{format(selectedDate, "MMM dd")}</span>
                          </>
                        ) : (
                          <>
                            <span className="hidden sm:inline">Select date</span>
                            <span className="sm:hidden">Date</span>
                          </>
                        )}
                      </span>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0 z-50" align="start" side="bottom">
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={(date) => {
                        if (date) {
                          setSelectedDate(date);
                          setIsDateCalendarOpen(false);
                        }
                      }}
                      disabled={isDateDisabled}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextDay}
                  disabled={isDateDisabled(addDays(selectedDate, 1))}
                  className="h-7 w-7 sm:h-8 sm:w-8 md:h-10 md:w-10 p-0 flex-shrink-0"
                  title="Next day"
                >
                  <ChevronRight className="h-3 w-3 md:h-4 md:w-4" />
                </Button>
              </div>
              
              <div className="text-xs md:text-sm text-muted-foreground text-center">
                <span className="hidden sm:inline">
                  {getBookingCountForDate(selectedDate)} of {getTotalDeskCount()} desks booked
                </span>
                <span className="sm:hidden">
                  {getBookingCountForDate(selectedDate)}/{getTotalDeskCount()} booked
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Floor Filter */}
        <Card className="shadow-clay-sm">
          <CardHeader className="pb-2 md:pb-3">
            <CardTitle className="flex items-center gap-2 text-sm sm:text-base md:text-lg">
              <Filter className="h-4 w-4 md:h-5 md:w-5" />
              Filter by Floor
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 sm:p-4 md:p-6">
            <div className="flex flex-wrap gap-1 sm:gap-2">
              <Button
                variant={selectedFloor === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFloor('all')}
                className="text-xs md:text-sm h-7 sm:h-8 md:h-9 px-2 sm:px-3"
              >
                <span className="hidden sm:inline">All Floors</span>
                <span className="sm:hidden">All</span>
              </Button>
              {availableFloors.map((floor) => (
                <Button
                  key={floor}
                  variant={selectedFloor === floor.toString() ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedFloor(floor.toString())}
                  className="text-xs md:text-sm h-7 sm:h-8 md:h-9 px-2 sm:px-3"
                >
                  <span className="hidden xs:inline">Floor {floor}</span>
                  <span className="xs:hidden">F{floor}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4 sm:space-y-6">
        {/* Floor and Zone Organization */}
        {Object.entries(filteredData).map(([floorNumber, zones]) => (
          <div key={floorNumber} className="space-y-3 sm:space-y-4">
            <div className="flex items-center gap-2 mb-3 sm:mb-4">
              <Building2 className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
              <h2 className="text-xl sm:text-2xl font-bold">Floor {floorNumber}</h2>
              <Badge variant="secondary" className="ml-2 text-xs">
                {Object.values(zones).reduce((total, zone) => total + zone.desks.length, 0)} desks
              </Badge>
            </div>
            
            <div className="grid gap-3 md:gap-4 lg:gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
              {Object.entries(zones).map(([zoneId, { zone, desks: zoneDesks }]) => (
                <Card key={zoneId} className="shadow-clay-sm border-l-4 border-l-primary/30">
                  <CardHeader className="pb-2 sm:pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 sm:gap-2 min-w-0">
                        <CardTitle className="text-sm sm:text-base lg:text-lg truncate">{zone.name}</CardTitle>
                        <button
                          onClick={() => handleZoneInfoClick(zone, zoneDesks)}
                          className="p-1 rounded-full hover:bg-muted transition-colors duration-200 flex-shrink-0"
                          title="View zone information"
                        >
                          <Info className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground hover:text-primary" />
                        </button>
                      </div>
                      <Badge variant="outline" className="text-xs flex-shrink-0">
                        {zoneDesks.length} desk{zoneDesks.length !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                    {zone.description && (
                      <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">{zone.description}</p>
                    )}
                  </CardHeader>
                  <CardContent className="p-3 sm:p-4 md:p-6">
                    <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-2 sm:gap-3 overflow-visible relative">
                      {zoneDesks.map((desk) => {
                        const status = getStatusForDate(desk, selectedDate);
                        const booking = getBookingForDate(desk, selectedDate);
                        const isOccupied = status === 'occupied' && booking?.users;
                        const styling = getEnhancedStatusStyling(status);
                        
                        return (
                          <button
                            key={desk.id}
                            onClick={() => handleDeskClickInternal(desk)}
                            className={cn(
                              // Base styling with responsive sizing
                              "group relative aspect-square rounded-lg sm:rounded-xl flex items-center justify-center",
                              "font-semibold text-white cursor-pointer",
                              "shadow-md hover:shadow-lg focus:shadow-lg sm:shadow-lg sm:hover:shadow-xl sm:focus:shadow-xl",
                              "ring-2 ring-transparent focus:ring-2 focus:ring-offset-2",
                              `focus:${styling.border}`,
                              // Background gradient
                              styling.background,
                              "focus:outline-none transition-all duration-200",
                              // Subtle pulse for occupied desks
                              isOccupied && "animate-[subtle-pulse_6s_ease-in-out_infinite]"
                            )}
                            title={isOccupied 
                              ? `${desk.name} - Click to view ${booking.users.name}'s profile`
                              : `${desk.name} - ${status} on ${format(selectedDate, "MMM dd")}`
                            }
                          >
                            {/* Status icon - responsive sizing */}
                            <div className="absolute top-1 right-1 sm:top-2 sm:right-2 opacity-80">
                              {React.cloneElement(styling.icon, { 
                                className: "w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4" 
                              })}
                            </div>
                            
                            {/* Main content */}
                            <div className="flex items-center justify-center w-full h-full p-0.5 sm:p-1">
                              {isOccupied ? (
                                <div className="relative cursor-pointer hover:scale-105 sm:hover:scale-110 transition-transform duration-200">
                                  <Avatar className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 xl:w-14 xl:h-14 ring-1 sm:ring-2 ring-white/30 shadow-sm sm:shadow-lg">
                                    <AvatarImage 
                                      src={booking.users.avatar} 
                                      alt={booking.users.name}
                                      className="object-cover"
                                    />
                                    <AvatarFallback className="text-[8px] xs:text-[9px] sm:text-[10px] md:text-xs lg:text-sm font-bold bg-white/20 text-white backdrop-blur-sm">
                                      {booking.users.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  {/* Online indicator - responsive sizing */}
                                  <div className="absolute -bottom-0.5 -right-0.5 w-1.5 h-1.5 sm:w-2 sm:h-2 md:w-2.5 md:h-2.5 lg:w-3 lg:h-3 xl:w-4 xl:h-4 bg-green-400 rounded-full border border-white sm:border-2 shadow-sm"></div>
                                </div>
                              ) : (
                                <div className="text-center px-0.5 sm:px-1">
                                  <div className="text-[8px] xs:text-[9px] sm:text-[10px] md:text-xs font-bold mb-0.5 leading-tight">
                                    {desk.name.replace('Desk ', '')}
                                  </div>
                                  <div className="text-[6px] xs:text-[7px] sm:text-[8px] md:text-[10px] opacity-80 capitalize leading-tight">
                                    {status}
                                  </div>
                                </div>
                              )}
                            </div>
                            
                            {/* Subtle pattern overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-lg sm:rounded-xl pointer-events-none"></div>
                            
                            {/* Hover glow effect */}
                            <div className="absolute inset-0 rounded-lg sm:rounded-xl bg-white/0 group-hover:bg-white/10 transition-colors duration-300 pointer-events-none"></div>
                          </button>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}

        {/* No results message */}
        {Object.keys(filteredData).length === 0 && (
          <Card className="shadow-clay-sm">
            <CardContent className="text-center py-6 sm:py-8">
              <Building2 className="h-10 w-10 sm:h-12 sm:w-12 mx-auto text-muted-foreground mb-3 sm:mb-4" />
              <p className="text-sm sm:text-base text-muted-foreground">
                No desks found for the selected floor.
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Zone Info Modal */}
      <Dialog open={isZoneInfoModalOpen} onOpenChange={setIsZoneInfoModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-primary" />
              Zone Information
            </DialogTitle>
          </DialogHeader>
          
          {selectedZoneInfo && (
            <div className="space-y-6">
              {/* Header Section with Photo */}
              <div className="relative">
                {selectedZoneInfo.photo ? (
                  <div className="relative h-48 w-full overflow-hidden rounded-lg bg-gradient-to-r from-blue-500 to-purple-600">
                    <img 
                      src={selectedZoneInfo.photo} 
                      alt={selectedZoneInfo.name}
                      className="h-full w-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <h2 className="text-2xl font-bold">{selectedZoneInfo.name}</h2>
                      <div className="flex items-center gap-2 mt-1">
                        <Building2 className="h-4 w-4" />
                        <span className="text-sm">Floor {selectedZoneInfo.floor_number}</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="relative h-48 w-full overflow-hidden rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <div className="text-center text-white">
                      <Building2 className="h-12 w-12 mx-auto mb-3 opacity-60" />
                      <h2 className="text-2xl font-bold">{selectedZoneInfo.name}</h2>
                      <div className="flex items-center justify-center gap-2 mt-1">
                        <Building2 className="h-4 w-4" />
                        <span className="text-sm">Floor {selectedZoneInfo.floor_number}</span>
                      </div>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
                  </div>
                )}
              </div>

              {/* Information Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700">
                  <CardContent className="p-4 text-center">
                    <Building2 className="h-8 w-8 mx-auto mb-2 text-blue-600 dark:text-blue-400" />
                    <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                      Floor {selectedZoneInfo.floor_number}
                    </div>
                    <div className="text-sm text-blue-600 dark:text-blue-300">
                      Floor Level
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700">
                  <CardContent className="p-4 text-center">
                    <User className="h-8 w-8 mx-auto mb-2 text-green-600 dark:text-green-400" />
                    <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                      {selectedZoneInfo.desks?.[0]?.count || 0}
                    </div>
                    <div className="text-sm text-green-600 dark:text-green-300">
                      Available Desks
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-700">
                  <CardContent className="p-4 text-center">
                    <CalendarIcon className="h-8 w-8 mx-auto mb-2 text-purple-600 dark:text-purple-400" />
                    <div className="text-xs font-semibold text-purple-900 dark:text-purple-100">
                      {format(new Date(selectedZoneInfo.created_at), 'MMM dd, yyyy')}
                    </div>
                    <div className="text-sm text-purple-600 dark:text-purple-300 mt-1">
                      Created
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Description Section */}
              {selectedZoneInfo.description && (
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                      <MapPin className="h-5 w-5 text-muted-foreground" />
                      Description
                    </h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {selectedZoneInfo.description}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Zone Status */}
              <div className="flex items-center justify-center">
                <Badge 
                  variant="secondary" 
                  className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2"
                >
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Zone Active
                </Badge>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
} 