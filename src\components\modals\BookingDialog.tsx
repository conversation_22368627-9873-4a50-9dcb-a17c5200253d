import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Calendar as CalendarIcon, AlertTriangle, Download } from 'lucide-react';
import { format } from 'date-fns';
import { DeskWithZone } from '@/types';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateICSFile, downloadICSFile } from '@/lib/calendar';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useState } from 'react';

interface BookingDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDesk: DeskWithZone | null;
  startDate?: Date;
  endDate?: Date;
  startTime: string;
  endTime: string;
  isStartCalendarOpen: boolean;
  isEndCalendarOpen: boolean;
  onStartDateChange: (date?: Date) => void;
  onEndDateChange: (date?: Date) => void;
  onStartTimeChange: (time: string) => void;
  onEndTimeChange: (time: string) => void;
  onStartCalendarOpenChange: (open: boolean) => void;
  onEndCalendarOpenChange: (open: boolean) => void;
  isDateDisabled: (date: Date) => boolean;
  hasTimeConflict: () => boolean;
  hasUserBookingConflict: () => boolean;
  isValidTimeRange: () => boolean;
  isValidDateRange: () => boolean;
  canSubmitBooking: () => boolean;
  onSubmit: (downloadCalendar?: boolean) => void;
  isSubmitting: boolean;
  timeOptions: { value: string; label: string }[];
}

export function BookingDialog({
  isOpen,
  onOpenChange,
  selectedDesk,
  startDate,
  endDate,
  startTime,
  endTime,
  isStartCalendarOpen,
  isEndCalendarOpen,
  onStartDateChange,
  onEndDateChange,
  onStartTimeChange,
  onEndTimeChange,
  onStartCalendarOpenChange,
  onEndCalendarOpenChange,
  isDateDisabled,
  hasTimeConflict,
  hasUserBookingConflict,
  isValidTimeRange,
  isValidDateRange,
  canSubmitBooking,
  onSubmit,
  isSubmitting,
  timeOptions,
}: BookingDialogProps) {
  const [downloadCalendar, setDownloadCalendar] = useState(false);

  if (!selectedDesk) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[600px] mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-base md:text-lg">
            <CalendarIcon className="h-4 w-4 md:h-5 md:w-5" />
            Book {selectedDesk.name}
          </DialogTitle>
          <DialogDescription className="text-sm">
            {selectedDesk.zone.name} • Floor {selectedDesk.zone.floor_number}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 px-1">
          {/* Date Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <Popover open={isStartCalendarOpen} onOpenChange={onStartCalendarOpenChange}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Select start date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 z-[60]" side="bottom" align="start">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => {
                      onStartDateChange(date);
                      onStartCalendarOpenChange(false);
                    }}
                    disabled={isDateDisabled}
                    initialFocus
                    defaultMonth={startDate}
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <Popover open={isEndCalendarOpen} onOpenChange={onEndCalendarOpenChange}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Select end date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 z-[60]" side="bottom" align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => {
                      onEndDateChange(date);
                      onEndCalendarOpenChange(false);
                    }}
                    disabled={(date) => isDateDisabled(date) || (startDate ? date < startDate : false)}
                    initialFocus
                    defaultMonth={endDate}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Time Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Time</label>
              <Select value={startTime} onValueChange={onStartTimeChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select start time" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] z-[60]">
                  {timeOptions.map((time) => (
                    <SelectItem key={time.value} value={time.value}>
                      {time.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">End Time</label>
              <Select value={endTime} onValueChange={onEndTimeChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select end time" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] z-[60]">
                  {timeOptions.map((time) => (
                    <SelectItem 
                      key={time.value} 
                      value={time.value}
                      disabled={time.value <= startTime}
                    >
                      {time.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Validation Messages */}
          {!isValidTimeRange() && startTime && endTime && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                End time must be after start time.
              </AlertDescription>
            </Alert>
          )}

          {!isValidDateRange() && startDate && endDate && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                End date must be on or after start date.
              </AlertDescription>
            </Alert>
          )}

          {hasTimeConflict() && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                This desk is already booked during the selected time period.
              </AlertDescription>
            </Alert>
          )}

          {hasUserBookingConflict() && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                You already have a booking during this time period.
              </AlertDescription>
            </Alert>
          )}

          {/* Booking Summary */}
          {startDate && endDate && startTime && endTime && isValidTimeRange() && isValidDateRange() && !hasTimeConflict() && !hasUserBookingConflict() && (
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Booking Summary</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <p>
                  <strong>Desk:</strong> {selectedDesk.name} ({selectedDesk.zone.name})
                </p>
                <p>
                  <strong>Duration:</strong> {startDate.toDateString() === endDate.toDateString() 
                    ? format(startDate, "PPP")
                    : `${format(startDate, "PPP")} - ${format(endDate, "PPP")}`
                  }
                </p>
                <p>
                  <strong>Time:</strong> {timeOptions.find(t => t.value === startTime)?.label} - {timeOptions.find(t => t.value === endTime)?.label}
                </p>
              </div>
              
              <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-800">
                <div className="flex items-center space-x-2 mb-3">
                  <Checkbox 
                    id="download-calendar" 
                    checked={downloadCalendar}
                    onCheckedChange={(checked) => setDownloadCalendar(checked as boolean)}
                  />
                  <Label htmlFor="download-calendar" className="text-sm">
                    Download calendar event after booking
                  </Label>
                </div>
                
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => {
                    const icsContent = generateICSFile({
                      deskName: selectedDesk.name,
                      zoneName: selectedDesk.zone.name,
                      startDate: format(startDate, 'yyyy-MM-dd'),
                      endDate: format(endDate, 'yyyy-MM-dd'),
                      startTime,
                      endTime,
                      userName: 'You'
                    });
                    
                    const filename = `desk-booking-${format(startDate, 'yyyy-MM-dd')}.ics`;
                    downloadICSFile(icsContent, filename);
                    toast.success('Calendar event downloaded!');
                  }}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Calendar Event
                </Button>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex-col gap-2 sm:flex-row pt-4">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button 
            onClick={() => onSubmit(downloadCalendar)}
            disabled={!canSubmitBooking() || isSubmitting}
            className="w-full sm:w-auto"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                <span className="hidden sm:inline">Booking...</span>
                <span className="sm:hidden">Booking...</span>
              </>
            ) : (
              <>
                <CalendarIcon className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Book This Desk</span>
                <span className="sm:hidden">Book Desk</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 